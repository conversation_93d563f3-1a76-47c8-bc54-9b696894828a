-- <PERSON><PERSON><PERSON> để thêm UserRoles cho 2 tà<PERSON> khoản admin và user
-- <PERSON><PERSON>y script này trong SQL Server Management Studio

-- <PERSON><PERSON><PERSON> tra các roles hiện có
SELECT * FROM AspNetRoles;

-- <PERSON><PERSON><PERSON> tra các users hiện có  
SELECT Id, User<PERSON><PERSON>, Email FROM AspNetUsers;

-- <PERSON><PERSON><PERSON> tra UserRoles hiện có
SELECT * FROM AspNetUserRoles;

-- Thêm role Admin <NAME_EMAIL>
-- Lưu ý: Thay thế RoleId bằng ID thực tế của role "Admin" từ bảng AspNetRoles
INSERT INTO AspNetUserRoles (UserId, RoleId)
SELECT 
    '21e70942-ee9c-4659-9baa-dfb40067f593', -- UserId của <EMAIL>
    r.Id -- RoleId của role "Admin"
FROM AspNetRoles r 
WHERE r.Name = 'Admin'
AND NOT EXISTS (
    SELECT 1 FROM AspNetUserRoles ur 
    WHERE ur.UserId = '21e70942-ee9c-4659-9baa-dfb40067f593' 
    AND ur.RoleId = r.Id
);

-- Thêm role User <NAME_EMAIL>
-- Lưu ý: Thay thế RoleId bằng ID thực tế của role "User" từ bảng AspNetRoles
INSERT INTO AspNetUserRoles (UserId, RoleId)
SELECT 
    '608d0f79-5518-4a1e-a8c1-9a73edd7cda7', -- UserId của <EMAIL>
    r.Id -- RoleId của role "User"
FROM AspNetRoles r 
WHERE r.Name = 'User'
AND NOT EXISTS (
    SELECT 1 FROM AspNetUserRoles ur 
    WHERE ur.UserId = '608d0f79-5518-4a1e-a8c1-9a73edd7cda7' 
    AND ur.RoleId = r.Id
);

-- Kiểm tra kết quả sau khi thêm
SELECT 
    u.UserName,
    u.Email,
    r.Name as RoleName
FROM AspNetUsers u
INNER JOIN AspNetUserRoles ur ON u.Id = ur.UserId
INNER JOIN AspNetRoles r ON ur.RoleId = r.Id
ORDER BY u.UserName;

-- Nếu chưa có roles, tạo roles trước:
-- INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
-- VALUES 
-- (NEWID(), 'Admin', 'ADMIN', NEWID()),
-- (NEWID(), 'User', 'USER', NEWID());
