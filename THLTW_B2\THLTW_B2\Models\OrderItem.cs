using System.ComponentModel.DataAnnotations;

namespace THLTW_B2.Models
{
    public class OrderItem
    {
        public int Id { get; set; }
        
        [Required]
        public int OrderId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
        public int Quantity { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal UnitPrice { get; set; }
        
        // Calculated property
        public decimal TotalPrice => Quantity * UnitPrice;
        
        // Navigation properties
        public Order? Order { get; set; }
        public Product? Product { get; set; }
    }
}
