@model IEnumerable<THLTW_B2.Models.CartItem>
@{
    ViewData["Title"] = "Giỏ hàng";
}

<!-- Page Header -->
<div class="bg-primary text-white py-4 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-0"><i class="fas fa-shopping-cart mr-2"></i>Giỏ hàng của bạn</h1>
            </div>
        </div>
    </div>
</div>

<div class="container">
    @if (Model.Any())
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Sản phẩm trong giỏ hàng</h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach (var item in Model)
                        {
                            <div class="row align-items-center p-3 border-bottom">
                                <div class="col-md-2">
                                    @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                    {
                                        <img src="@item.Product.ImageUrl" alt="@item.Product.Name" class="img-fluid rounded" style="max-height: 80px;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-4">
                                    <h6 class="mb-1">@item.Product.Name</h6>
                                    <small class="text-muted">@item.Product.Category?.Name</small>
                                </div>
                                <div class="col-md-2">
                                    <span class="fw-bold text-primary">@item.Product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(@item.Id, @(item.Quantity - 1))">-</button>
                                        <input type="text" class="form-control text-center" value="@item.Quantity" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(@item.Id, @(item.Quantity + 1))">+</button>
                                    </div>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="fw-bold">@((item.Product.Price * item.Quantity).ToString("C0", new System.Globalization.CultureInfo("vi-VN")))</div>
                                    <button class="btn btn-sm btn-outline-danger mt-1" onclick="removeFromCart(@item.Id)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        }
                    </div>
                </div>
                
                <div class="mt-3">
                    <form asp-action="ClearCart" method="post" onsubmit="return confirm('Bạn có chắc muốn xóa toàn bộ giỏ hàng?')">
                        <button type="submit" class="btn btn-outline-danger">
                            <i class="fas fa-trash mr-2"></i>Xóa toàn bộ giỏ hàng
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tóm tắt đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        @{
                            var totalAmount = Model.Sum(item => item.Product.Price * item.Quantity);
                            var totalItems = Model.Sum(item => item.Quantity);
                        }
                        
                        <div class="d-flex justify-content-between mb-2">
                            <span>Số lượng sản phẩm:</span>
                            <span>@totalItems</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span>@totalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary">@totalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</strong>
                        </div>
                        
                        <a href="@Url.Action("Checkout", "Order")" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-credit-card mr-2"></i>Thanh toán
                        </a>
                        
                        <a href="@Url.Action("Index", "Product")" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-arrow-left mr-2"></i>Tiếp tục mua sắm
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
            <h3>Giỏ hàng của bạn đang trống</h3>
            <p class="text-muted">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục mua sắm.</p>
            <a href="@Url.Action("Index", "Product")" class="btn btn-primary btn-lg">
                <i class="fas fa-shopping-bag mr-2"></i>Bắt đầu mua sắm
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        function updateQuantity(cartItemId, quantity) {
            if (quantity <= 0) {
                removeFromCart(cartItemId);
                return;
            }
            
            $.post('@Url.Action("UpdateQuantity", "Cart")', { cartItemId: cartItemId, quantity: quantity })
                .done(function() {
                    location.reload();
                })
                .fail(function() {
                    alert('Có lỗi xảy ra!');
                });
        }
        
        function removeFromCart(cartItemId) {
            if (confirm('Bạn có chắc muốn xóa sản phẩm này?')) {
                $.post('@Url.Action("RemoveFromCart", "Cart")', { cartItemId: cartItemId })
                    .done(function() {
                        location.reload();
                    })
                    .fail(function() {
                        alert('Có lỗi xảy ra!');
                    });
            }
        }
    </script>
}
