@model THLTW_B2.Models.Category

@{
    ViewData["Title"] = "Thêm <PERSON>";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Thêm <PERSON></h1>
                <a asp-action="Categories" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Quay Lại
                </a>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Thông Tin Danh Mục</h6>
        </div>
        <div class="card-body">
            <form asp-action="CreateCategory" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <div class="form-group">
                    <label asp-for="Name" class="form-label">Tê<PERSON></label>
                    <input asp-for="Name" class="form-control" placeholder="Nhập tên danh mục">
                    <span asp-validation-for="Name" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>Lưu Danh Mục
                    </button>
                    <a asp-action="Categories" class="btn btn-secondary ml-2">
                        <i class="fas fa-times mr-2"></i>Hủy
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
