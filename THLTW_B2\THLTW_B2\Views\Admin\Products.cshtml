@model IEnumerable<THLTW_B2.Models.Product>

@{
    ViewData["Title"] = "Quản Lý Sản Phẩm";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Quản Lý Sản Phẩm</h1>
                <a asp-action="CreateProduct" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>Thêm Sản Phẩm Mới
                </a>
            </div>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Dan<PERSON>ẩm</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Hình Ảnh</th>
                            <th>Tên Sản Phẩm</th>
                            <th>Danh Mục</th>
                            <th>Giá</th>
                            <th>Mô Tả</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model)
                        {
                            <tr>
                                <td>@product.Id</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(product.ImageUrl))
                                    {
                                        <img src="@product.ImageUrl" alt="@product.Name" style="width: 50px; height: 50px; object-fit: cover;" class="rounded">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    }
                                </td>
                                <td>@product.Name</td>
                                <td>@product.Category?.Name</td>
                                <td>@product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</td>
                                <td>
                                    @if (product.Description.Length > 50)
                                    {
                                        @(product.Description.Substring(0, 50) + "...")
                                    }
                                    else
                                    {
                                        @product.Description
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="EditProduct" asp-route-id="@product.Id" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(@product.Id, '@product.Name')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Xác Nhận Xóa</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Bạn có chắc chắn muốn xóa sản phẩm <strong id="productName"></strong>?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Xóa</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(productId, productName) {
            document.getElementById('productName').textContent = productName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteProduct", "Admin")/' + productId;
            $('#deleteModal').modal('show');
        }
    </script>
}
