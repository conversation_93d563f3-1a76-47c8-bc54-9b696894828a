@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Trang chủ";
}

<!-- Hero Section -->
<div class="bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold">THLTW Store</h1>
                <p class="lead">Cửa hàng công nghệ hàng đầu với những sản phẩm chất lượng cao và giá cả hợp lý</p>
                <a href="#products" class="btn btn-light btn-lg">
                    <i class="fas fa-shopping-cart mr-2"></i>Mua sắm ngay
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-laptop fa-10x opacity-50"></i>
            </div>
        </div>
    </div>
</div>

<!-- Featured Products Section -->
<section id="products" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">Sản phẩm <span class="text-primary">nổi bật</span></h2>
            <p class="lead text-muted">Khám phá những sản phẩm công nghệ mới nhất với giá tốt nhất</p>
        </div>
        <div class="row">
            @foreach (var product in Model.Take(6))
            {
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background-color: #f8f9fa;">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" alt="@product.Name" class="img-fluid" style="max-height: 180px; max-width: 100%;" />
                            }
                            else
                            {
                                <i class="fas fa-image fa-3x text-muted"></i>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text text-muted small">@product.Category?.Name</p>
                            <p class="card-text flex-grow-1">@product.Description.Substring(0, Math.Min(100, product.Description.Length))...</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary mb-0">@product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> Xem
                                        </button>
                                        <button type="button" class="btn btn-sm btn-primary">
                                            <i class="fas fa-cart-plus"></i> Mua
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="text-center mt-5">
            <a href="#" class="btn btn-primary btn-lg">
                <i class="fas fa-eye mr-2"></i>Xem tất cả sản phẩm
            </a>
        </div>
    </div>
</section>
