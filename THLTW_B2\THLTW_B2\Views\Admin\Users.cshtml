@model IEnumerable<Microsoft.AspNetCore.Identity.IdentityUser>

@{
    ViewData["Title"] = "Quản L<PERSON> D<PERSON>";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4 text-gray-800">Qu<PERSON><PERSON></h1>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary"><PERSON><PERSON>ch <PERSON>ờ<PERSON> Dùng</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Email</th>
                            <th>Tên <PERSON>ng <PERSON>p</th>
                            <th>Email <PERSON>c <PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model)
                        {
                            <tr>
                                <td>@user.Id.Substring(0, 8)...</td>
                                <td>@user.Email</td>
                                <td>@user.UserName</td>
                                <td>
                                    @if (user.EmailConfirmed)
                                    {
                                        <span class="badge badge-success">Đã xác thực</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-warning">Chưa xác thực</span>
                                    }
                                </td>
                                <td>
                                    @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.Now)
                                    {
                                        <span class="text-danger">Bị khóa</span>
                                    }
                                    else
                                    {
                                        <span class="text-success">Hoạt động</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewUser('@user.Id')">
                                            <i class="fas fa-eye"></i> Xem
                                        </button>
                                        @if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTimeOffset.Now)
                                        {
                                            <button type="button" class="btn btn-sm btn-success" onclick="unlockUser('@user.Id')">
                                                <i class="fas fa-unlock"></i> Mở khóa
                                            </button>
                                        }
                                        else
                                        {
                                            <button type="button" class="btn btn-sm btn-warning" onclick="lockUser('@user.Id')">
                                                <i class="fas fa-lock"></i> Khóa
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewUser(userId) {
            // Implement view user details
            alert('Xem chi tiết người dùng: ' + userId);
        }

        function lockUser(userId) {
            if (confirm('Bạn có chắc chắn muốn khóa người dùng này?')) {
                // Implement lock user
                alert('Khóa người dùng: ' + userId);
            }
        }

        function unlockUser(userId) {
            if (confirm('Bạn có chắc chắn muốn mở khóa người dùng này?')) {
                // Implement unlock user
                alert('Mở khóa người dùng: ' + userId);
            }
        }
    </script>
}
