@model THLTW_B2.Models.Order
@{
    ViewData["Title"] = "Chi tiết đơn hàng";
}

<!-- Page Header -->
<div class="bg-primary text-white py-4 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-0"><i class="fas fa-receipt mr-2"></i>Chi tiết đơn hàng #@Model.Id</h1>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <!-- Thông tin đơn hàng -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Thông tin đơn hàng</h5>
                    @{
                        string statusClass = Model.Status switch
                        {
                            "Pending" => "badge bg-warning text-dark",
                            "Processing" => "badge bg-info",
                            "Shipped" => "badge bg-primary",
                            "Delivered" => "badge bg-success",
                            "Cancelled" => "badge bg-danger",
                            _ => "badge bg-secondary"
                        };
                        
                        string statusText = Model.Status switch
                        {
                            "Pending" => "Chờ xử lý",
                            "Processing" => "Đang xử lý",
                            "Shipped" => "Đã gửi hàng",
                            "Delivered" => "Đã giao hàng",
                            "Cancelled" => "Đã hủy",
                            _ => Model.Status
                        };
                    }
                    <span class="@statusClass">@statusText</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Mã đơn hàng:</strong> #@Model.Id</p>
                            <p><strong>Ngày đặt:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                            <p><strong>Tổng tiền:</strong> <span class="text-primary fw-bold">@Model.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Người nhận:</strong> @Model.ShippingName</p>
                            <p><strong>Số điện thoại:</strong> @Model.ShippingPhone</p>
                            <p><strong>Địa chỉ:</strong> @Model.ShippingAddress</p>
                            @if (!string.IsNullOrEmpty(Model.Notes))
                            {
                                <p><strong>Ghi chú:</strong> @Model.Notes</p>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chi tiết sản phẩm -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Sản phẩm đã đặt</h5>
                </div>
                <div class="card-body p-0">
                    @if (Model.OrderItems != null && Model.OrderItems.Any())
                    {
                        @foreach (var item in Model.OrderItems)
                        {
                            <div class="row align-items-center p-3 border-bottom">
                                <div class="col-md-2">
                                    @if (!string.IsNullOrEmpty(item.Product?.ImageUrl))
                                    {
                                        <img src="@item.Product.ImageUrl" alt="@item.Product.Name" class="img-fluid rounded" style="max-height: 80px;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 80px;">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-5">
                                    <h6 class="mb-1">@item.Product?.Name</h6>
                                    <small class="text-muted">@item.Product?.Category?.Name</small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="fw-bold">@item.UnitPrice.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                </div>
                                <div class="col-md-1 text-center">
                                    <span><EMAIL></span>
                                </div>
                                <div class="col-md-2 text-end">
                                    <span class="fw-bold text-primary">@((item.UnitPrice * item.Quantity).ToString("C0", new System.Globalization.CultureInfo("vi-VN")))</span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="p-3 text-center text-muted">
                            Không có sản phẩm nào trong đơn hàng này.
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thao tác</h5>
                </div>
                <div class="card-body">
                    <a href="@Url.Action("Index", "Order")" class="btn btn-outline-primary w-100 mb-2">
                        <i class="fas fa-list mr-2"></i>Xem tất cả đơn hàng
                    </a>
                    
                    @if (Model.Status == "Pending")
                    {
                        <form asp-action="CancelOrder" method="post" onsubmit="return confirm('Bạn có chắc muốn hủy đơn hàng này?')">
                            <input type="hidden" name="id" value="@Model.Id" />
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-times mr-2"></i>Hủy đơn hàng
                            </button>
                        </form>
                    }
                    
                    <a href="@Url.Action("Index", "Product")" class="btn btn-outline-secondary w-100 mt-2">
                        <i class="fas fa-shopping-bag mr-2"></i>Tiếp tục mua sắm
                    </a>
                </div>
            </div>

            <!-- Tóm tắt thanh toán -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Tóm tắt thanh toán</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tạm tính:</span>
                        <span>@Model.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Phí vận chuyển:</span>
                        <span class="text-success">Miễn phí</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Tổng cộng:</strong>
                        <strong class="text-primary">@Model.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
