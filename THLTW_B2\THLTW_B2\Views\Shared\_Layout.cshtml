@using Microsoft.AspNetCore.Identity
@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - THLTW Store</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="@Url.Action("Index", "Home")">
                <i class="fas fa-store mr-2"></i>THLTW Store
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Index">Trang chủ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Product" asp-action="Index">Sản phẩm</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="About">Giới thiệu</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Liên hệ</a>
                    </li>
                </ul>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    @if (SignInManager.IsSignedIn(User))
                    {
                        <!-- Giỏ hàng -->
                        <li class="nav-item">
                            <a class="nav-link position-relative" asp-controller="Cart" asp-action="Index">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                                    0
                                </span>
                            </a>
                        </li>

                        <!-- Đơn hàng -->
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Order" asp-action="Index">
                                <i class="fas fa-receipt mr-1"></i>Đơn hàng
                            </a>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user mr-1"></i>@(User.Identity?.Name ?? "User")
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                    <i class="fas fa-user-cog mr-2"></i>Quản lý tài khoản
                                </a></li>
                                
                                @if (User.IsInRole("Admin"))
                                {
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Quản trị viên</h6></li>
                                    <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Admin" asp-action="Products">
                                        <i class="fas fa-box mr-2"></i>Quản lý sản phẩm
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Admin" asp-action="Categories">
                                        <i class="fas fa-tags mr-2"></i>Quản lý danh mục
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Admin" asp-action="Orders">
                                        <i class="fas fa-shopping-cart mr-2"></i>Quản lý đơn hàng
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Admin" asp-action="Users">
                                        <i class="fas fa-users mr-2"></i>Quản lý người dùng
                                    </a></li>
                                }
                                
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    }
                    else
                    {
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Account" asp-action="Login">
                                <i class="fas fa-sign-in-alt mr-1"></i>Đăng nhập
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Account" asp-action="Register">
                                <i class="fas fa-user-plus mr-1"></i>Đăng ký
                            </a>
                        </li>
                    }
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-store mr-2"></i>THLTW Store</h5>
                    <p>Cửa hàng công nghệ uy tín, chất lượng cao với giá tốt nhất thị trường.</p>
                </div>
                <div class="col-md-3">
                    <h6>Liên kết nhanh</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-white-50 text-decoration-none">Trang chủ</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Sản phẩm</a></li>
                        <li><a href="#" class="text-white-50 text-decoration-none">Liên hệ</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>Liên hệ</h6>
                    <p class="text-white-50 mb-1"><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                    <p class="text-white-50 mb-1"><i class="fas fa-phone mr-2"></i>0123 456 789</p>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 THLTW Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @if (SignInManager.IsSignedIn(User))
    {
        <script>
            // Cập nhật số lượng giỏ hàng
            function updateCartCount() {
                $.get('@Url.Action("GetCartCount", "Cart")', function(count) {
                    $('#cart-count').text(count);
                    if (count > 0) {
                        $('#cart-count').show();
                    } else {
                        $('#cart-count').hide();
                    }
                });
            }

            // Gọi khi trang load
            $(document).ready(function() {
                updateCartCount();
            });

            // Hàm thêm vào giỏ hàng (có thể gọi từ các trang khác)
            window.addToCart = function(productId, quantity = 1) {
                $.post('@Url.Action("AddToCart", "Cart")', { productId: productId, quantity: quantity })
                    .done(function() {
                        updateCartCount();
                        // Hiển thị thông báo thành công
                        showNotification('Đã thêm sản phẩm vào giỏ hàng!', 'success');
                    })
                    .fail(function() {
                        showNotification('Có lỗi xảy ra!', 'error');
                    });
            };

            // Hàm hiển thị thông báo
            function showNotification(message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const notification = `
                    <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                         style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                $('body').append(notification);

                // Tự động ẩn sau 3 giây
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 3000);
            }
        </script>
    }

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
