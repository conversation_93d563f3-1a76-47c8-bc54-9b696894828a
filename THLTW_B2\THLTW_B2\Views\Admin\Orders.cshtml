@model IEnumerable<THLTW_B2.Models.Order>

@{
    ViewData["Title"] = "Quản Lý Đơn Hàng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4 text-gray-800">Quản Lý Đơn Hàng</h1>
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    }

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Danh Sách Đơn Hàng</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON><PERSON></th>
                            <th>Ngày Đặt</th>
                            <th>Tổng Tiền</th>
                            <th>Trạng Thái</th>
                            <th>Thao Tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr>
                                <td>#@order.Id</td>
                                <td>@order.ShippingName</td>
                                <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                <td>@order.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</td>
                                <td>
                                    @switch (order.Status)
                                    {
                                        case "Pending":
                                            <span class="badge badge-warning">Chờ xử lý</span>
                                            break;
                                        case "Processing":
                                            <span class="badge badge-info">Đang xử lý</span>
                                            break;
                                        case "Shipped":
                                            <span class="badge badge-primary">Đã gửi</span>
                                            break;
                                        case "Delivered":
                                            <span class="badge badge-success">Đã giao</span>
                                            break;
                                        case "Cancelled":
                                            <span class="badge badge-danger">Đã hủy</span>
                                            break;
                                        default:
                                            <span class="badge badge-secondary">@order.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-info" onclick="viewOrder(@order.Id)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-warning dropdown-toggle" data-toggle="dropdown">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <div class="dropdown-menu">
                                                <form asp-action="UpdateOrderStatus" method="post" style="display: inline;">
                                                    <input type="hidden" name="orderId" value="@order.Id" />
                                                    <input type="hidden" name="status" value="Processing" />
                                                    <button type="submit" class="dropdown-item">Đang xử lý</button>
                                                </form>
                                                <form asp-action="UpdateOrderStatus" method="post" style="display: inline;">
                                                    <input type="hidden" name="orderId" value="@order.Id" />
                                                    <input type="hidden" name="status" value="Shipped" />
                                                    <button type="submit" class="dropdown-item">Đã gửi</button>
                                                </form>
                                                <form asp-action="UpdateOrderStatus" method="post" style="display: inline;">
                                                    <input type="hidden" name="orderId" value="@order.Id" />
                                                    <input type="hidden" name="status" value="Delivered" />
                                                    <button type="submit" class="dropdown-item">Đã giao</button>
                                                </form>
                                                <form asp-action="UpdateOrderStatus" method="post" style="display: inline;">
                                                    <input type="hidden" name="orderId" value="@order.Id" />
                                                    <input type="hidden" name="status" value="Cancelled" />
                                                    <button type="submit" class="dropdown-item text-danger">Hủy đơn</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function viewOrder(orderId) {
            // Implement view order details
            alert('Xem chi tiết đơn hàng #' + orderId);
        }
    </script>
}
