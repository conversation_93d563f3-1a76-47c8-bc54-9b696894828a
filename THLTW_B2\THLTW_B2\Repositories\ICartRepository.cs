using THLTW_B2.Models;

namespace THLTW_B2.Repositories
{
    public interface ICartRepository
    {
        Task<IEnumerable<CartItem>> GetCartItemsByUserIdAsync(string userId);
        Task<IEnumerable<CartItem>> GetCartItemsAsync(string userId); // Alias cho GetCartItemsByUserIdAsync
        Task<CartItem?> GetCartItemAsync(string userId, int productId);
        Task AddToCartAsync(CartItem cartItem);
        Task AddToCartAsync(string userId, int productId, int quantity); // Overload method
        Task UpdateCartItemAsync(CartItem cartItem);
        Task UpdateQuantityAsync(int cartItemId, int quantity); // Method mới
        Task RemoveFromCartAsync(string userId, int productId);
        Task RemoveFromCartAsync(int cartItemId); // Overload method
        Task ClearCartAsync(string userId);
        Task<int> GetCartItemCountAsync(string userId);
        Task<int> GetCartCountAsync(string userId); // <PERSON>as cho GetCartItemCountAsync
        Task<decimal> GetCartTotalAsync(string userId);
    }
}
