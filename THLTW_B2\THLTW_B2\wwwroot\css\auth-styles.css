/* Authentication Menu Styles */
.navbar-nav .dropdown-menu {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-width: 200px;
}

.navbar-nav .dropdown-item {
    color: #333;
    padding: 8px 16px;
    text-decoration: none;
    display: block;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.navbar-nav .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #333;
}

.navbar-nav .dropdown-toggle::after {
    margin-left: 0.5em;
}

/* Custom styles for navigation links - Override any existing styles */
.custom_nav-container .navbar-nav .nav-link,
.navbar-nav .nav-link,
.navbar-nav li a {
    color: #333 !important;
    font-weight: 500;
    padding: 8px 15px !important;
    transition: all 0.3s ease;
    text-decoration: none;
}

.custom_nav-container .navbar-nav .nav-link:hover,
.navbar-nav .nav-link:hover,
.navbar-nav li a:hover {
    color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
}

/* Active nav item */
.custom_nav-container .navbar-nav .nav-item.active .nav-link,
.navbar-nav .nav-item.active .nav-link {
    color: #007bff !important;
    font-weight: 600;
}

/* Authentication specific links */
.navbar-nav .nav-link[href*="Login"],
.navbar-nav .nav-link[href*="Register"] {
    background-color: #007bff;
    color: #fff !important;
    border-radius: 4px;
    margin: 0 2px;
}

.navbar-nav .nav-link[href*="Login"]:hover,
.navbar-nav .nav-link[href*="Register"]:hover {
    background-color: #0056b3;
    color: #fff !important;
}

/* Dropdown toggle for user menu */
.navbar-nav .dropdown-toggle {
    color: #333 !important;
    font-weight: 500;
}

.navbar-nav .dropdown-toggle:hover {
    color: #007bff !important;
}

/* Ensure all navigation text is visible - High priority overrides */
.header_section .navbar-nav .nav-link,
.header_section .navbar-nav .dropdown-toggle,
.header_section .custom_nav-container .navbar-nav .nav-link,
.header_section .custom_nav-container .navbar-nav .dropdown-toggle,
.header_section .navbar-nav li a,
.header_section .navbar-nav li .nav-link {
    color: #333 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Force visibility for all nav items */
.navbar-nav li,
.navbar-nav .nav-item {
    opacity: 1 !important;
    visibility: visible !important;
}

.navbar-nav li a,
.navbar-nav .nav-item .nav-link {
    color: #333 !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .navbar-nav .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
    
    .navbar-nav .dropdown-item {
        color: #fff;
    }
    
    .navbar-nav .dropdown-item:hover {
        background-color: rgba(255,255,255,0.1);
        color: #fff;
    }
}
