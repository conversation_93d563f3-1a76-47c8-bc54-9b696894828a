using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace THLTW_B2.Models
{
    public class Order
    {
        public int Id { get; set; }
        
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        [Required]
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Tổng tiền phải lớn hơn 0")]
        public decimal TotalAmount { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Processing, Shipped, Delivered, Cancelled
        
        // Thông tin giao hàng
        [Required]
        [StringLength(100)]
        public string ShippingName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(15)]
        public string ShippingPhone { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string ShippingAddress { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        // Navigation properties
        public IdentityUser? User { get; set; }
        public List<OrderItem>? OrderItems { get; set; }
    }
}
