@model IEnumerable<THLTW_B2.Models.Order>
@{
    ViewData["Title"] = "Lịch sử đơn hàng";
}

<!-- Page Header -->
<div class="bg-primary text-white py-4 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-0"><i class="fas fa-receipt mr-2"></i>L<PERSON>ch sử đơn hàng</h1>
            </div>
        </div>
    </div>
</div>

<div class="container">
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var order in Model.OrderByDescending(o => o.OrderDate))
            {
                <div class="col-12 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">Đơn hàng #@order.Id</h6>
                                <small class="text-muted">@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</small>
                            </div>
                            <div>
                                @{
                                    string statusClass = order.Status switch
                                    {
                                        "Pending" => "badge bg-warning text-dark",
                                        "Processing" => "badge bg-info",
                                        "Shipped" => "badge bg-primary",
                                        "Delivered" => "badge bg-success",
                                        "Cancelled" => "badge bg-danger",
                                        _ => "badge bg-secondary"
                                    };
                                    
                                    string statusText = order.Status switch
                                    {
                                        "Pending" => "Chờ xử lý",
                                        "Processing" => "Đang xử lý",
                                        "Shipped" => "Đã gửi hàng",
                                        "Delivered" => "Đã giao hàng",
                                        "Cancelled" => "Đã hủy",
                                        _ => order.Status
                                    };
                                }
                                <span class="@statusClass">@statusText</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>Thông tin giao hàng:</h6>
                                    <p class="mb-1"><strong>Người nhận:</strong> @order.ShippingName</p>
                                    <p class="mb-1"><strong>Số điện thoại:</strong> @order.ShippingPhone</p>
                                    <p class="mb-1"><strong>Địa chỉ:</strong> @order.ShippingAddress</p>
                                    @if (!string.IsNullOrEmpty(order.Notes))
                                    {
                                        <p class="mb-1"><strong>Ghi chú:</strong> @order.Notes</p>
                                    }
                                </div>
                                <div class="col-md-4 text-end">
                                    <h5 class="text-primary">@order.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</h5>
                                    <div class="mt-2">
                                        <a href="@Url.Action("Details", new { id = order.Id })" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye mr-1"></i>Chi tiết
                                        </a>
                                        @if (order.Status == "Pending")
                                        {
                                            <form asp-action="CancelOrder" method="post" class="d-inline" onsubmit="return confirm('Bạn có chắc muốn hủy đơn hàng này?')">
                                                <input type="hidden" name="id" value="@order.Id" />
                                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                                    <i class="fas fa-times mr-1"></i>Hủy
                                                </button>
                                            </form>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-5x text-muted mb-3"></i>
            <h3>Bạn chưa có đơn hàng nào</h3>
            <p class="text-muted">Hãy bắt đầu mua sắm để tạo đơn hàng đầu tiên.</p>
            <a href="@Url.Action("Index", "Product")" class="btn btn-primary btn-lg">
                <i class="fas fa-shopping-bag mr-2"></i>Bắt đầu mua sắm
            </a>
        </div>
    }
</div>
