@model THLTW_B2.Models.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container" style="margin-top: 100px; margin-bottom: 100px;">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h3>Đ<PERSON>ng ký</h3>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu (ít nhất 6 ký tự)" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">Đăng ký</button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Đã có tài khoản? <a asp-action="Login" asp-route-returnUrl="@ViewData["ReturnUrl"]">Đăng nhập ngay</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
