using Microsoft.AspNetCore.Identity;
using THLTW_B2.Models;

namespace THLTW_B2.DataAccess
{
    public static class DbSeeder
    {
        public static async Task SeedAsync(ApplicationDbContext context, UserManager<IdentityUser> userManager)
        {
            // Seed Categories
            if (!context.Categories.Any())
            {
                var categories = new List<Category>
                {
                    new Category { Name = "Điện thoại" },
                    new Category { Name = "Laptop" },
                    new Category { Name = "Tablet" },
                    new Category { Name = "Phụ kiện" },
                    new Category { Name = "Đồng hồ thông minh" }
                };

                context.Categories.AddRange(categories);
                await context.SaveChangesAsync();
            }

            // Seed Products
            if (!context.Products.Any())
            {
                var products = new List<Product>
                {
                    new Product
                    {
                        Name = "iPhone 15 Pro Max",
                        Price = 29990000,
                        Description = "iPhone 15 Pro Max với chip A17 Pro mạnh mẽ, camera 48MP chuyên nghiệp",
                        CategoryId = 1,
                        ImageUrl = "/images/iphone15.jpg"
                    },
                    new Product
                    {
                        Name = "Samsung Galaxy S24 Ultra",
                        Price = 26990000,
                        Description = "Galaxy S24 Ultra với S Pen tích hợp, camera zoom 100x",
                        CategoryId = 1,
                        ImageUrl = "/images/galaxy-s24.jpg"
                    },
                    new Product
                    {
                        Name = "MacBook Pro M3",
                        Price = 45990000,
                        Description = "MacBook Pro 14 inch với chip M3, hiệu năng vượt trội",
                        CategoryId = 2,
                        ImageUrl = "/images/macbook-m3.jpg"
                    },
                    new Product
                    {
                        Name = "Dell XPS 13",
                        Price = 32990000,
                        Description = "Laptop Dell XPS 13 mỏng nhẹ, màn hình InfinityEdge",
                        CategoryId = 2,
                        ImageUrl = "/images/dell-xps13.jpg"
                    },
                    new Product
                    {
                        Name = "iPad Pro 12.9",
                        Price = 24990000,
                        Description = "iPad Pro 12.9 inch với chip M2, hỗ trợ Apple Pencil",
                        CategoryId = 3,
                        ImageUrl = "/images/ipad-pro.jpg"
                    },
                    new Product
                    {
                        Name = "AirPods Pro 2",
                        Price = 5990000,
                        Description = "AirPods Pro thế hệ 2 với chống ồn chủ động",
                        CategoryId = 4,
                        ImageUrl = "/images/airpods-pro2.jpg"
                    },
                    new Product
                    {
                        Name = "Apple Watch Series 9",
                        Price = 8990000,
                        Description = "Apple Watch Series 9 với chip S9, màn hình sáng hơn",
                        CategoryId = 5,
                        ImageUrl = "/images/apple-watch-s9.jpg"
                    }
                };

                context.Products.AddRange(products);
                await context.SaveChangesAsync();
            }

            // Seed Admin User
            if (!userManager.Users.Any())
            {
                var adminUser = new IdentityUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true
                };

                await userManager.CreateAsync(adminUser, "Admin@123");

                var testUser = new IdentityUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmailConfirmed = true
                };

                await userManager.CreateAsync(testUser, "User@123");
            }
        }
    }
}
