@model THLTW_B2.Models.LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container" style="margin-top: 100px; margin-bottom: 100px;">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h3>Đăng nhập</h3>
                </div>
                <div class="card-body">
                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label"></label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">Đăng nhập</button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p>Chưa có tài khoản? <a asp-action="Register" asp-route-returnUrl="@ViewData["ReturnUrl"]">Đăng ký ngay</a></p>
                    </div>
                </div>
            </div>

            <!-- Demo Accounts Info -->
            <div class="card mt-3 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle mr-2"></i>Tài Khoản Demo</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>Admin:</strong><br>
                            <small><EMAIL></small><br>
                            <small>Admin@123</small>
                        </div>
                        <div class="col-6">
                            <strong>User:</strong><br>
                            <small><EMAIL></small><br>
                            <small>User@123</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
