@model THLTW_B2.Models.Order
@{
    ViewData["Title"] = "Thanh toán";
}

<!-- <PERSON> Header -->
<div class="bg-primary text-white py-4 mb-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-0"><i class="fas fa-credit-card mr-2"></i>Thanh toán</h1>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <form asp-action="PlaceOrder" method="post">
        <div class="row">
            <!-- Thông tin giao hàng -->
            <div class="col-lg-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Thông tin giao hàng</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="ShippingName" class="form-label">Họ và tên <span class="text-danger">*</span></label>
                                <input asp-for="ShippingName" class="form-control" required />
                                <span asp-validation-for="ShippingName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ShippingPhone" class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                                <input asp-for="ShippingPhone" class="form-control" required />
                                <span asp-validation-for="ShippingPhone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label asp-for="ShippingAddress" class="form-label">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                            <textarea asp-for="ShippingAddress" class="form-control" rows="3" required></textarea>
                            <span asp-validation-for="ShippingAddress" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">Ghi chú</label>
                            <textarea asp-for="Notes" class="form-control" rows="2" placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Phương thức thanh toán -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Phương thức thanh toán</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="paymentMethod" id="cod" value="cod" checked>
                            <label class="form-check-label" for="cod">
                                <i class="fas fa-money-bill-wave mr-2"></i>Thanh toán khi nhận hàng (COD)
                            </label>
                        </div>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="radio" name="paymentMethod" id="bank" value="bank" disabled>
                            <label class="form-check-label text-muted" for="bank">
                                <i class="fas fa-credit-card mr-2"></i>Chuyển khoản ngân hàng (Sắp có)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tóm tắt đơn hàng -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Tóm tắt đơn hàng</h5>
                    </div>
                    <div class="card-body">
                        @if (ViewBag.CartItems != null)
                        {
                            @foreach (var item in (IEnumerable<THLTW_B2.Models.CartItem>)ViewBag.CartItems)
                            {
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <h6 class="mb-0">@item.Product.Name</h6>
                                        <small class="text-muted">Số lượng: @item.Quantity</small>
                                    </div>
                                    <span class="fw-bold">@((item.Product.Price * item.Quantity).ToString("C0", new System.Globalization.CultureInfo("vi-VN")))</span>
                                </div>
                            }
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tạm tính:</span>
                                <span>@Model.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Phí vận chuyển:</span>
                                <span class="text-success">Miễn phí</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Tổng cộng:</strong>
                                <strong class="text-primary">@Model.TotalAmount.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</strong>
                            </div>
                        }
                        
                        <button type="submit" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-check mr-2"></i>Đặt hàng
                        </button>
                        
                        <a href="@Url.Action("Index", "Cart")" class="btn btn-outline-secondary w-100 mt-2">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại giỏ hàng
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
