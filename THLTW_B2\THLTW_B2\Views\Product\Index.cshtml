﻿@using Microsoft.AspNetCore.Identity
@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Sản phẩm";
}

<!-- Page Header -->
<div class="bg-primary text-white py-4 mb-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h3 mb-0"><i class="fas fa-box mr-2"></i>Tất cả sản phẩm</h1>
                <p class="mb-0">Khám phá bộ sưu tập sản phẩm công nghệ của chúng tôi</p>
            </div>
            <div class="col-md-4 text-end">
                @if (User.IsInRole("Admin"))
                {
                    <a href="@Url.Action("Add", "Product")" class="btn btn-light">
                        <i class="fas fa-plus mr-2"></i>Thêm sản phẩm
                    </a>
                }
            </div>
        </div>
    </div>
</div>

<div class="container">
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var product in Model)
            {
                <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
                    <div class="card h-100 shadow-sm product-card">
                        <div class="card-img-top d-flex align-items-center justify-content-center" style="height: 200px; background-color: #f8f9fa;">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" alt="@product.Name" class="img-fluid" style="max-height: 180px; max-width: 100%;" />
                            }
                            else
                            {
                                <i class="fas fa-image fa-3x text-muted"></i>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">@product.Name</h6>
                            <p class="card-text text-muted small">@product.Category?.Name</p>
                            <p class="card-text flex-grow-1 small">@(product.Description.Length > 80 ? product.Description.Substring(0, 80) + "..." : product.Description)</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="h6 text-primary mb-0">@product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                </div>
                                <div class="btn-group w-100">
                                    <a href="@Url.Action("Display", "Product", new { id = product.Id })" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> Xem
                                    </a>
                                    @if (SignInManager.IsSignedIn(User))
                                    {
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addToCart(@product.Id)">
                                            <i class="fas fa-cart-plus"></i> Mua
                                        </button>
                                    }
                                    else
                                    {
                                        <a href="@Url.Action("Login", "Account")" class="btn btn-primary btn-sm">
                                            <i class="fas fa-sign-in-alt"></i> Đăng nhập
                                        </a>
                                    }
                                </div>

                                @if (User.IsInRole("Admin"))
                                {
                                    <div class="btn-group w-100 mt-2">
                                        <a href="@Url.Action("Update", "Product", new { id = product.Id })" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i> Sửa
                                        </a>
                                        <a href="@Url.Action("Delete", "Product", new { id = product.Id })" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i> Xóa
                                        </a>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-5x text-muted mb-3"></i>
            <h3>Chưa có sản phẩm nào</h3>
            <p class="text-muted">Hiện tại chưa có sản phẩm nào trong cửa hàng.</p>
            @if (User.IsInRole("Admin"))
            {
                <a href="@Url.Action("Add", "Product")" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus mr-2"></i>Thêm sản phẩm đầu tiên
                </a>
            }
        </div>
    }
</div>

@section Styles {
    <style>
        .product-card {
            transition: transform 0.2s ease-in-out;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }
    </style>
}
