using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using THLTW_B2.Models;
using THLTW_B2.Repositories;

namespace THLTW_B2.Controllers
{
    [Authorize]
    public class OrderController : Controller
    {
        private readonly IOrderRepository _orderRepository;
        private readonly ICartRepository _cartRepository;
        private readonly UserManager<IdentityUser> _userManager;

        public OrderController(
            IOrderRepository orderRepository,
            ICartRepository cartRepository,
            UserManager<IdentityUser> userManager)
        {
            _orderRepository = orderRepository;
            _cartRepository = cartRepository;
            _userManager = userManager;
        }

        // Xem lịch sử đơn hàng của user
        public async Task<IActionResult> Index()
        {
            var userId = _userManager.GetUserId(User);
            var orders = await _orderRepository.GetOrdersByUserIdAsync(userId);
            return View(orders);
        }

        // Xem chi tiết đơn hàng
        public async Task<IActionResult> Details(int id)
        {
            var userId = _userManager.GetUserId(User);
            var order = await _orderRepository.GetByIdAsync(id);
            
            // Kiểm tra quyền truy cập - user chỉ xem được đơn hàng của mình
            if (order == null || (order.UserId != userId && !User.IsInRole("Admin")))
            {
                return NotFound();
            }

            return View(order);
        }

        // Trang checkout
        public async Task<IActionResult> Checkout()
        {
            var userId = _userManager.GetUserId(User);
            var cartItems = await _cartRepository.GetCartItemsAsync(userId);
            
            if (!cartItems.Any())
            {
                TempData["Error"] = "Giỏ hàng của bạn đang trống!";
                return RedirectToAction("Index", "Cart");
            }

            var order = new Order
            {
                UserId = userId,
                OrderDate = DateTime.Now,
                Status = "Pending",
                TotalAmount = cartItems.Sum(item => item.Product.Price * item.Quantity)
            };

            ViewBag.CartItems = cartItems;
            return View(order);
        }

        // Xử lý đặt hàng
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PlaceOrder(Order order)
        {
            var userId = _userManager.GetUserId(User);
            var cartItems = await _cartRepository.GetCartItemsAsync(userId);
            
            if (!cartItems.Any())
            {
                TempData["Error"] = "Giỏ hàng của bạn đang trống!";
                return RedirectToAction("Index", "Cart");
            }

            // Tạo đơn hàng
            order.UserId = userId;
            order.OrderDate = DateTime.Now;
            order.Status = "Pending";
            order.TotalAmount = cartItems.Sum(item => item.Product.Price * item.Quantity);

            // Tạo order items từ cart items
            order.OrderItems = cartItems.Select(item => new OrderItem
            {
                ProductId = item.ProductId,
                Quantity = item.Quantity,
                UnitPrice = item.Product.Price
            }).ToList();

            if (ModelState.IsValid)
            {
                await _orderRepository.AddAsync(order);
                
                // Xóa giỏ hàng sau khi đặt hàng thành công
                await _cartRepository.ClearCartAsync(userId);
                
                TempData["Success"] = "Đặt hàng thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.";
                return RedirectToAction("Details", new { id = order.Id });
            }

            ViewBag.CartItems = cartItems;
            return View("Checkout", order);
        }

        // Hủy đơn hàng (chỉ khi đơn hàng ở trạng thái Pending)
        [HttpPost]
        public async Task<IActionResult> CancelOrder(int id)
        {
            var userId = _userManager.GetUserId(User);
            var order = await _orderRepository.GetByIdAsync(id);
            
            if (order == null || order.UserId != userId)
            {
                return NotFound();
            }

            if (order.Status != "Pending")
            {
                TempData["Error"] = "Không thể hủy đơn hàng này!";
                return RedirectToAction("Details", new { id });
            }

            order.Status = "Cancelled";
            await _orderRepository.UpdateAsync(order);
            
            TempData["Success"] = "Đã hủy đơn hàng thành công!";
            return RedirectToAction("Index");
        }
    }
}
