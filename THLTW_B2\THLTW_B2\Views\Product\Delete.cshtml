﻿@model THLTW_B2.Models.Product
@{
	ViewData["Title"] = "Delete Product";
	Layout = "~/Views/Shared/_Layout.cshtml";
}
<h2>Are you sure you want to delete this?</h2>
<div>
	<h4>Name: @Model.Name</h4>
	<h4>Price: @Model.Price</h4>
	<h4>Description: @Model.Description</h4>
</div>
<form asp-action="DeleteConfirmed" method="post">
	<input type="hidden" asp-for="Id" />
	<input type="submit" value="Delete" class="btn btn-danger" /> |
	<a asp-action="Index">Cancel</a>
</form>