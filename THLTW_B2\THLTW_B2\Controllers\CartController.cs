using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using THLTW_B2.Models;
using THLTW_B2.Repositories;

namespace THLTW_B2.Controllers
{
    [Authorize]
    public class CartController : Controller
    {
        private readonly ICartRepository _cartRepository;
        private readonly IProductRepository _productRepository;
        private readonly UserManager<IdentityUser> _userManager;

        public CartController(
            ICartRepository cartRepository,
            IProductRepository productRepository,
            UserManager<IdentityUser> userManager)
        {
            _cartRepository = cartRepository;
            _productRepository = productRepository;
            _userManager = userManager;
        }

        // Xem giỏ hàng
        public async Task<IActionResult> Index()
        {
            var userId = _userManager.GetUserId(User);
            var cartItems = await _cartRepository.GetCartItemsAsync(userId);
            return View(cartItems);
        }

        // Thêm sản phẩm vào giỏ hàng
        [HttpPost]
        public async Task<IActionResult> AddToCart(int productId, int quantity = 1)
        {
            var userId = _userManager.GetUserId(User);
            var product = await _productRepository.GetByIdAsync(productId);
            
            if (product == null)
            {
                TempData["Error"] = "Sản phẩm không tồn tại!";
                return RedirectToAction("Index", "Product");
            }

            await _cartRepository.AddToCartAsync(userId, productId, quantity);
            TempData["Success"] = $"Đã thêm {product.Name} vào giỏ hàng!";
            
            return RedirectToAction("Index", "Product");
        }

        // Cập nhật số lượng trong giỏ hàng
        [HttpPost]
        public async Task<IActionResult> UpdateQuantity(int cartItemId, int quantity)
        {
            if (quantity <= 0)
            {
                return await RemoveFromCart(cartItemId);
            }

            await _cartRepository.UpdateQuantityAsync(cartItemId, quantity);
            TempData["Success"] = "Đã cập nhật số lượng!";
            
            return RedirectToAction("Index");
        }

        // Xóa sản phẩm khỏi giỏ hàng
        [HttpPost]
        public async Task<IActionResult> RemoveFromCart(int cartItemId)
        {
            await _cartRepository.RemoveFromCartAsync(cartItemId);
            TempData["Success"] = "Đã xóa sản phẩm khỏi giỏ hàng!";
            
            return RedirectToAction("Index");
        }

        // Xóa toàn bộ giỏ hàng
        [HttpPost]
        public async Task<IActionResult> ClearCart()
        {
            var userId = _userManager.GetUserId(User);
            await _cartRepository.ClearCartAsync(userId);
            TempData["Success"] = "Đã xóa toàn bộ giỏ hàng!";
            
            return RedirectToAction("Index");
        }

        // Lấy số lượng items trong giỏ hàng (AJAX)
        [HttpGet]
        public async Task<IActionResult> GetCartCount()
        {
            var userId = _userManager.GetUserId(User);
            var count = await _cartRepository.GetCartCountAsync(userId);
            return Json(count);
        }
    }
}
