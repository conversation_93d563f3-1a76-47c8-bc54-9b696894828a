@model THLTW_B2.Models.Product

@{
    ViewData["Title"] = "Chỉnh Sửa Sản Phẩm";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Chỉnh Sửa Sản <PERSON>ẩ<PERSON></h1>
                <a asp-action="Products" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Quay Lại
                </a>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Thông Tin Sản Phẩm</h6>
        </div>
        <div class="card-body">
            <form asp-action="EditProduct" method="post">
                <input asp-for="Id" type="hidden">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Name" class="form-label">Tên Sản Phẩm</label>
                            <input asp-for="Name" class="form-control" placeholder="Nhập tên sản phẩm">
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="CategoryId" class="form-label">Danh Mục</label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.Categories">
                                <option value="">-- Chọn danh mục --</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Price" class="form-label">Giá (VNĐ)</label>
                            <input asp-for="Price" class="form-control" type="number" step="1000" placeholder="Nhập giá sản phẩm">
                            <span asp-validation-for="Price" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="ImageUrl" class="form-label">URL Hình Ảnh</label>
                            <input asp-for="ImageUrl" class="form-control" placeholder="Nhập URL hình ảnh">
                            <span asp-validation-for="ImageUrl" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label asp-for="Description" class="form-label">Mô Tả</label>
                    <textarea asp-for="Description" class="form-control" rows="4" placeholder="Nhập mô tả sản phẩm"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i>Cập Nhật Sản Phẩm
                    </button>
                    <a asp-action="Products" class="btn btn-secondary ml-2">
                        <i class="fas fa-times mr-2"></i>Hủy
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
