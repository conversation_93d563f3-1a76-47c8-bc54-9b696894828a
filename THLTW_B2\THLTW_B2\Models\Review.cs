using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace THLTW_B2.Models
{
    public class Review
    {
        public int Id { get; set; }
        
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(1, 5, ErrorMessage = "Đánh giá phải từ 1 đến 5 sao")]
        public int Rating { get; set; }
        
        [StringLength(1000)]
        public string? Comment { get; set; }
        
        public DateTime ReviewDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public IdentityUser? User { get; set; }
        public Product? Product { get; set; }
    }
}
