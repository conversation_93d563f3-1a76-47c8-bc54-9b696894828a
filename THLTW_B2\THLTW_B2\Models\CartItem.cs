using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace THLTW_B2.Models
{
    public class CartItem
    {
        public int Id { get; set; }
        
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
        public int Quantity { get; set; }
        
        public DateTime DateAdded { get; set; } = DateTime.Now;
        
        // Navigation properties
        public IdentityUser? User { get; set; }
        public Product? Product { get; set; }
    }
}
